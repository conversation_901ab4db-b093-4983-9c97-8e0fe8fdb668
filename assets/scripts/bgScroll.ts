import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('bgScroll')
export class test extends Component {

    // 背景是由同一张背景图上下无缝拼接而成，这张是上方的图
    @property(Node)
    public bgUpper:Node = null;

    // 这是下方的图
    @property(Node)
    public bgDowner:Node = null;

    @property
    speed:number = 100;

    start() {
 
    }

    update(deltaTime: number) {
        let position01 = this.bgUpper.position;
        this.bgUpper.setPosition(position01.x, position01.y - this.speed * deltaTime, position01.z);

        let position02 = this.bgDowner.position;
        this.bgDowner.setPosition(position02.x, position02.y - this.speed * deltaTime, position02.z);

        let curUpperPosition = this.bgUpper.position;
        let curDownerPosition = this.bgDowner.position;

        // 852指的是图片的纵向像素长度，当其中一张图向下移动（y为负值即向下）且值已超过852，则将其纵坐标➕852
        if (this.bgUpper.position.y < -852) {
            this.bgUpper.setPosition(curDownerPosition.x, curDownerPosition.y + 852, position01.z);
        }
        if (this.bgDowner.position.y < -852) {
            this.bgDowner.setPosition(curUpperPosition.x, curUpperPosition.y + 852, position02.z);
        }
    }
}

