import { _decorator, Component, Input, input, Node, EventTouch } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('player')
export class player extends Component {

    protected onLoad(): void {
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
    }

    protected onDestroy(): void {
        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
    }

    onTouchMove(event: EventTouch) {
        const position = this.node.position;
        this.node.setPosition(position.x + event.getDeltaX(), position.y + event.getDeltaY(), position.z);
    }

}

